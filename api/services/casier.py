from datetime import datetime
from typing import List, Optional
from sqlalchemy import func
from models.business import <PERSON><PERSON><PERSON>, LotExpediteur, Site
from constants.enumerations import StatutLotExpediteurEnum


class ServiceCasier:
    """Service pour gérer les casiers de stockage des lots d'enveloppes."""
    
    @staticmethod
    def obtenir_casier_disponible(site: Site) -> Optional[Casier]:
        """Attribue un casier disponible à un lot d'expéditeur."""
        
        from core.db import get_session
        with get_session() as session:
            # Rechercher un casier disponible (pas en maintenance et pas associé à un lot)
            casier = session.query(Casier).filter(
                ~Casier.enveloppes.any(),
                Casier.site_id == site.id,
                ~Casier.lot_expediteur.has()
            ).order_by(Casier.id).first()
            
            if not casier:
                # Aucun casier disponible
                return None
            
            # Marquer le casier comme occupé et l'associer au lot
            casier.date_attribution = datetime.utcnow()
            
            return casier
    