import json
import redis
import os
from typing import Any, Callable, Optional, TypeVar

T = TypeVar('T')

class RedisCache:
    @staticmethod
    def get_or_set(
        key: str, 
        expiration_days: int, 
        method: Callable[[], T],
        no_cache: bool = False
    ) -> T:
        """
        Récupère une valeur depuis Redis ou exécute la méthode fournie si la clé n'existe pas.
        
        Args:
            key: Clé unique pour stocker les données dans Redis
            expiration_days: Durée d'expiration du cache en jours
            method: Fonction à exécuter si la clé n'existe pas dans le cache
            no_cache: Si True, ignore le cache et force l'exécution de la méthode
            
        Returns:
            La valeur mise en cache ou le résultat de la méthode
        """
        # Connexion à Redis
        redis_host = os.getenv("REDIS_HOST", "localhost")
        redis_port = int(os.getenv("REDIS_PORT", "6379"))
        redis_password = os.getenv("REDIS_PASSWORD", None)
        
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=True
        )
        
        try:
            # Vérifier si les données sont en cache
            if not no_cache:
                cached_data = redis_client.get(key)
                if cached_data:
                    print("cache data")
                    return json.loads(cached_data)
            
            # Si pas en cache ou no_cache=True, exécuter la méthode
            result = method()
            
            # Convertir jours en secondes pour Redis
            expiration_seconds = expiration_days * 24 * 60 * 60
            
            # Stocker dans Redis avec expiration
            redis_client.set(
                key,
                json.dumps(result),
                ex=expiration_seconds
            )
            
            return result
            
        except redis.RedisError:
            # En cas d'erreur Redis, exécuter la méthode sans cache
            return method()