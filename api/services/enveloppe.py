from constants.enumerations import StatutVerificationEnum, StatutEnveloppeEnum, ProduitEnum, TypeAffranchissementEnum, CategorieAffranchissementEnum, DestinationEnveloppeEnum
from constants.variables import CONSTANTES_VALORISATION, CONSTANTES_PRIX_AFFRANCHISSEMENT
from datetime import datetime
from services.produits import ServiceProduits
from models.business import Affranchissement, PhotoEnveloppe
from models.public import AffranchissementItemPublic
    
class ServiceEnveloppe:
    
    def __init__(self, enveloppe):
        self.enveloppe = enveloppe

    def complet(self) -> bool:
        """
        Une enveloppe est complète si :
        - Elle a au moins 3 photos
        - Tous ses affranchissements sont complets
        """
        # Vérification du nombre minimum de photos
        if len(self.enveloppe.photos) < 1:
            return False
        
        # if len(self.enveloppe.affranchissements) == 0:
        #     return False
            
        # Vérification que tous les affranchissements sont complets
        return len(self.affranchissements_non_complets()) == 0

    def prix_affranchissements_valide(self):
        """
        Retourne la somme des prix des affranchissements valides en €
        """
        valides_affranchissements = [affranchissement for affranchissement in self.enveloppe.affranchissements if affranchissement.statut != StatutVerificationEnum.INVALIDE]
                
        return sum(affranchissement.prix_unite_euros * affranchissement.quantite for affranchissement in valides_affranchissements)
        
    def valoriser(self):
        """
        Effectue la valorisation de l'enveloppe
        """
        
        # Section 1: Postage : cout de l'enveloppe - prix des affranchissements valides
        cout_enveloppe = self.calcul_frais_postaux_dus()
        prix_affranchissements_valide = self.prix_affranchissements_valide()
        cout_a_recuperer = max(0, cout_enveloppe - prix_affranchissements_valide)
        
        nb_affranchissements_invalides = len(self.affranchissements_invalides())
        
        presence_affranchissements_invalide = nb_affranchissements_invalides > 0
        presence_taxe = cout_a_recuperer > 0
        
        # Section 2: Livraison : taxe de fraude si présence d'affranchissements invalides + cout d'envoi
        # == CAS A
        coeff_taxe_livraison = CONSTANTES_VALORISATION.coeff_taxe_livraison_si_fraude if presence_affranchissements_invalide else 0
        taxe_livraison_a_recuperer = cout_a_recuperer * coeff_taxe_livraison
        taxe_livraison_fixe = CONSTANTES_VALORISATION.taxe_livraison_fixe_si_fraude if presence_taxe else 0
        taxe_livraison_totale = taxe_livraison_a_recuperer + taxe_livraison_fixe
        total_a_recuperer_livraison = cout_a_recuperer + taxe_livraison_totale

        # Section 3: Collecte : le fraudeur vient chercher l'enveloppe
        # == CAS B
        total_a_recuperer_collecte_ht = CONSTANTES_VALORISATION.frais_collecte_ht
        total_a_recuperer_collecte_tva = CONSTANTES_VALORISATION.frais_collecte_ht * CONSTANTES_VALORISATION.coeff_collecte_tva
        total_a_recuperer_collecte_ttc = total_a_recuperer_collecte_ht + total_a_recuperer_collecte_tva
        
        # Section 4: Expédition : retour, on renvoit au fraudeur l'enveloppe
        # == CAS C
        frais_expédition = self.calcul_cout_envoi()
        total_a_recuperer_expédition_ht = total_a_recuperer_collecte_ht + frais_expédition
        total_a_recuperer_expédition_tva = total_a_recuperer_collecte_tva
        total_a_recuperer_expédition_ttc = total_a_recuperer_expédition_ht + total_a_recuperer_expédition_tva

        # Mise à jour de la valorisation avec les détails
        self.enveloppe.valorisation = ({
            'postage': {
                'cout_enveloppe': cout_enveloppe,
                'cout_affranchissements_valide': prix_affranchissements_valide,
                'nb_affranchissements_invalides': nb_affranchissements_invalides,
                'montant_sous_affranchissement': cout_a_recuperer,
                'presence_affranchissements_invalide': presence_affranchissements_invalide,
                'presence_taxe': presence_taxe,
                'coeff_taxe_livraison': coeff_taxe_livraison,
            },
            'livraison': {
                'taxe_livraison_a_recuperer': taxe_livraison_a_recuperer,
                'taxe_livraison_fixe': taxe_livraison_fixe,
                'taxe_livraison_totale': taxe_livraison_totale,
                'cout_total': total_a_recuperer_livraison # cout + taxes
            },
            'collecte': {
                'frais_collecte_ht': CONSTANTES_VALORISATION.frais_collecte_ht,
                'coeff_collecte_tva': CONSTANTES_VALORISATION.coeff_collecte_tva,
                'cout_ht': total_a_recuperer_collecte_ht,
                'cout_tva': total_a_recuperer_collecte_tva,
                'cout_ttc': total_a_recuperer_collecte_ttc
            },
            'expédition': {
                'frais_expédition': frais_expédition,
                'cout_ht': total_a_recuperer_expédition_ht,
                'cout_tva': total_a_recuperer_expédition_tva,
                'cout_ttc': total_a_recuperer_expédition_ttc
            }
        })
        
        #self.enveloppe.statut = StatutEnveloppeEnum.VALORISE
        self.enveloppe.updated_at = datetime.now()        
        
        
    def calcul_frais_postaux_dus(self):
        """
        Combien coute l'enveloppe ? en affranchissements € cout théorique de l'enveloppe
        """
        # ligne grille correspondant au produit et à son poids

        # Si pas hors gabarit, on prend le tarif de la lettre verte
        if self.enveloppe.destination_enveloppe in [DestinationEnveloppeEnum.INTERNATIONAL]:
            if not self.enveloppe.surdimensionne or not self.enveloppe.surpoids:
                price = ServiceProduits.recherche_produit_proche(produit=ProduitEnum.LINT, poids=self.enveloppe.poids)
            else:
                price = ServiceProduits.recherche_produit_proche(produit=ProduitEnum.PPI, poids=self.enveloppe.poids)
        else:
            price = ServiceProduits.recherche_produit_proche(destination=self.enveloppe.destination_enveloppe, 
                                                        poids=self.enveloppe.poids)
        postage_due = None

        # si pas de surpoids ni hors gabarit, on prend le tarif courrier
        if not self.enveloppe.surpoids and not self.enveloppe.surdimensionne:
            # on regarde si on a des affranchissements SD86, 87
            has_affran_sd = any(
                element.categorie == CategorieAffranchissementEnum.CODE and
                element.type == TypeAffranchissementEnum.SD and
                element.origine in ["86", "87", None]
                for element in self.enveloppe.affranchissements
            )
        
            # on regarde si on a des affranchissements CAB
            has_affran_not_sd = any(
                element.categorie != CategorieAffranchissementEnum.CODE
                for element in self.enveloppe.affranchissements
            )

            # Regarde si on a que des affranchissements CODE
            has_only_affran_sd = has_affran_sd and not has_affran_not_sd

            # on prend le tarif MTEL (MonTimbreEnLigne) s'il n'y a que de l'affran SD, Tarif postal classique SINON
            postage_due = price.courrier_mtel if has_only_affran_sd else price.courrier_tp

            # on ajoute le prix du suivi s'il y a présence : SD88
            has_sd88 = any(
                element.type == TypeAffranchissementEnum.SD and element.origine == "88"
                for element in self.enveloppe.affranchissements
            )

            #TODO: Changer car on utilise plus la nature 

            # Cas où on a un SD sans international avec suivi
            has_sd_sans_inter_avec_suivi = any(
                element.type == TypeAffranchissementEnum.SD and
                element.nature_instance is not None and
                element.nature_instance.produit is not None and
                element.nature_instance.produit.destination == DestinationEnveloppeEnum.METROPOLE and
                element.nature_instance.suivi is True and
                element.origine in ["86", "87", None]
                for element in self.enveloppe.affranchissements
            )

            has_sd_avec_inter_avec_suivi = any(
                element.type == TypeAffranchissementEnum.SD and
                element.nature_instance is not None and
                element.nature_instance.produit is not None and
                element.nature_instance.produit.destination == DestinationEnveloppeEnum.INTERNATIONAL and
                element.nature_instance.suivi is True and
                element.origine in ["86", "87", None]
                for element in self.enveloppe.affranchissements
            )


            # Cas où on a un S10 L
            has_s10 = any(
                element.type == TypeAffranchissementEnum.S10 and
                element.code is not None and
                len(element.code.strip()) > 1 and
                element.code.strip().upper()[0] == "L"
                for element in self.enveloppe.affranchissements
            )


            # Debug all
            # print("has_s10", has_s10)
            # print("has_sd88", has_sd88)
            # print("has_sd_sans_inter_avec_suivi", has_sd_sans_inter_avec_suivi)
            # print("has_sd_avec_inter_avec_suivi", has_sd_avec_inter_avec_suivi)

            # print("postagedue", postage_due)

            if self.enveloppe.destination_enveloppe in [DestinationEnveloppeEnum.INTERNATIONAL]:
                # cas où on ajoute un suivi international
                if has_s10 or has_sd_avec_inter_avec_suivi:
                    postage_due += CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10
            else:
                # cas où on ajout un suivi national
                if has_sd88 or has_sd_sans_inter_avec_suivi:
                    postage_due += CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_sd88
        else:
            # sinon on prend tarif colissimo selon zone de la destination            
            zone = self.enveloppe.destination.zone if self.enveloppe.destination else "FM"
            
            zone_mapping = {
                "FM": price.colissimo_zone_fm,
                "OM1": price.colissimo_zone_om1,
                "OM2": price.colissimo_zone_om2,
                "A": price.colissimo_zone_a,
                "B": price.colissimo_zone_b,
                "C": price.colissimo_zone_c
            }
            postage_due = zone_mapping.get(zone, price.colissimo_zone_fm)

            # on ajoute le supplément
            postage_due += self.enveloppe.destination.supplement if self.enveloppe.destination and self.enveloppe.destination.supplement else 0

        return postage_due
    
    def calcul_cout_envoi(self) -> float:
        """
        Calcul le cout de l'envoi en fonction du poids. (envoi en retour à l'expediteur)
        On utilise le ProduitEnum.LV car c'est le produit par défaut pour l'envoi.
        """
        if not self.enveloppe.surdimensionne or not self.enveloppe.surpoids:
            price = ServiceProduits.recherche_produit_proche(produit=ProduitEnum.LV, poids=self.enveloppe.poids)
        else:
            price = ServiceProduits.recherche_produit_proche(produit=ProduitEnum.PPI, poids=self.enveloppe.poids)
        
        # Si pas de surpoids ni hors gabarit, on prend le tarif courrier
        if not self.enveloppe.surpoids and not self.enveloppe.surdimensionne:
            return price.courrier_tp
        
        return price.colissimo_zone_fm

    def affranchissements_non_complets(self) -> list[Affranchissement]:
        """
        Retourne les affranchissements invalides : permet de savoir si l'enveloppe est complète
        """
        affranchissements_non_complets = [
            affranchissement for affranchissement in self.enveloppe.affranchissements
            if not affranchissement.informations.complet
        ]
        
        return affranchissements_non_complets
        
    def est_sous_affranchi(self) -> bool:
        """
        Retourne True si l'enveloppe est sous-affranchi
        """
        return self.calcul_frais_postaux_dus() > self.prix_affranchissements_valide()
    
    def statut_finalisation(self) -> StatutEnveloppeEnum:
        """
        Retourne le statut de finalisation de l'enveloppe
        """
        # Enveloppe encore en EDITION
        if self.enveloppe.statut == StatutEnveloppeEnum.EDITION:
            return
        
        # Vérification des affranchissements invalides
        if len(self.affranchissements_invalides()) > 0:
            self.enveloppe.statut = StatutEnveloppeEnum.FRAUDULEUSE
            return
        
        # Vérification du sous-affranchissement
        if self.est_sous_affranchi():
            self.enveloppe.statut = StatutEnveloppeEnum.SOUS_AFFRANCHI
            return
        
        self.enveloppe.statut = StatutEnveloppeEnum.TERMINEE
        
    def affranchissements_invalides(self) -> list[Affranchissement]:
        """
        Retourne les affranchissements invalides
        """
        return [affranchissement for affranchissement in self.enveloppe.affranchissements if affranchissement.statut == StatutVerificationEnum.INVALIDE]
    
    def informations(self) -> dict:
        """
        Retourne un dictionnaire avec tous les calculs pour l'enveloppe
        """
        return {
            "complet": self.complet(),
            "prix_affranchissements_valide": self.prix_affranchissements_valide(),
            "modifiable": True,#self.enveloppe.statut in [StatutEnveloppeEnum.EDITION],
            "nombre_affranchissements_invalides": len(self.affranchissements_invalides())
        }

    def on_save(self):
        self.enveloppe.informations_data = self.informations()
        self.enveloppe.surpoids = self.enveloppe.poids > 2000  

        # Si enveloppe != EDITION, on valorise
        if self.enveloppe.statut == StatutEnveloppeEnum.EDITION:
            return
        
        self.valoriser()
        self.statut_finalisation()


    def ajouter_photo(self, file):
        """
        Ajoute une photo à l'enveloppe
        """
        # Upload vers Azure Blob Storage
        from services.storage import StorageService
        contents = file.file.read()
        blob_path = f"enveloppes/{self.enveloppe.uuid}/{file.filename}"
        url = StorageService.upload_file(contents, blob_path, content_type=file.content_type)
        
        # Création de l'entrée PhotoEnveloppe
        photo = PhotoEnveloppe(
            format=file.content_type,
            enveloppe_id=self.enveloppe.id,
            url=url  # Ajout de l'URL pour pouvoir accéder à la photo
        )
        self.enveloppe.photos.append(photo)
        self.enveloppe.updated_at = datetime.now()

    @classmethod
    def enveloppe_en_edition(cls, user, session):
        from models.business import Enveloppe

        # Recherche de l'enveloppe en mode édition pour l'utilisateur courant
        enveloppe = session.query(Enveloppe).filter(
            Enveloppe.user_id == user.id,
            Enveloppe.statut == StatutEnveloppeEnum.EDITION
        ).first()

        # Si aucune enveloppe n'est trouvée, en créer une nouvelle
        if not enveloppe:
            enveloppe = Enveloppe(
                created_at=datetime.now(),
                updated_at=datetime.now(),
                statut=StatutEnveloppeEnum.EDITION,
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                user_id=user.id,
                site_id=user.site_id
            )

            # Récupère la dernière enveloppe terminée pour copier le produit
            derniere_enveloppe = session.query(Enveloppe).filter(
                Enveloppe.user_id == user.id,
                Enveloppe.statut != StatutEnveloppeEnum.EDITION
            ).order_by(Enveloppe.created_at.desc()).first()

            if derniere_enveloppe:
                enveloppe.destination_enveloppe = derniere_enveloppe.destination_enveloppe

            # Utilisateur de Roissy, on force le produit à LV
            if user.site.nom.upper() == "ROISSY":
                enveloppe.destination_enveloppe = DestinationEnveloppeEnum.INTERNATIONAL

        enveloppe.site_id = user.site_id

        session.add(enveloppe)
        session.commit()
        session.refresh(enveloppe)

        return enveloppe