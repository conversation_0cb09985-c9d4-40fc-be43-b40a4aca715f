from datetime import datetime
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from models.business import Envelop<PERSON>, Exped<PERSON>ur, LotExpediteur, <PERSON><PERSON><PERSON>, RegleMetier, User
from constants.enumerations import TypeRegleMetierEnum, StatutCasierEnum, StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum, StatutEnveloppeEnum
from services.casier import ServiceCasier
from fastapi import HTTPException



class ServiceLotExpediteur:
    STATUT_CONCERNES = [StatutEnveloppeEnum.FRAUDULEUSE, StatutEnveloppeEnum.SOUS_AFFRANCHI]
    
    def __init__(self, session: Session):
        self.session = session

    def lot_expediteur(self, enveloppe: Enveloppe):
        """
        Pour une Enveloppe et son Expéditeur, retourne le LotExpediteur associé et le Casier disponible.
        Si l'enveloppe est frauduleuse, crée un nouveau lot si nécessaire ou retourne le lot existant.
        """
        if not enveloppe or not enveloppe.expediteur_id or enveloppe.casier_id is not None:
            return

        if enveloppe.statut not in self.STATUT_CONCERNES:
            return 

        # possibilité d'avoir 2 lots ouverts pour 2 site différents
        # Recherche un lot existant pour le site de l'utilisateur courant
        lot = self.lot_existant_pour(enveloppe)
        
        # Pas de lot existant, on crée
        if not lot:
            if self.verifier_besoin_creation_lot(enveloppe.expediteur_id):
                lot = self.creer_lot_expediteur(enveloppe)
            else:
                return
    

        # Ajouter l'enveloppe au lot existant
        enveloppe.casier = lot.casier
        enveloppe.lot_expediteur = lot
        self.session.commit()

        # Vérifie si on a rempli le Casier
        # self._verifier_capacite_casier(casier, lot, enveloppe.site)
    
    def _verifier_capacite_casier(self, casier: Casier, lot: LotExpediteur, site):
        """Vérifie si le casier est plein et en obtient un nouveau si nécessaire."""
        nb_enveloppes = self.session.query(func.count(Enveloppe.id)).filter(
            Enveloppe.casier_id == casier.id
        ).scalar() 
        
        if nb_enveloppes >= casier.capacite_max:
            casier.statut = StatutCasierEnum.PLEIN
            # Obtenir un nouveau casier
            lot.casier = ServiceCasier.obtenir_casier_disponible(site)
        
        self.session.commit()

    def verifier_besoin_creation_lot(self, expediteur_id: int) -> bool:
        """Vérifie si un lot doit être créé pour un Expéditeur."""
        count = self.session.query(func.count(Enveloppe.id)).filter(
            Enveloppe.expediteur_id == expediteur_id,
            Enveloppe.statut.in_(self.STATUT_CONCERNES)
        ).scalar()

        seuil = RegleMetier.valeur_pour(self.session, 
                                        cle="SEUIL_LOT_EXPEDITEUR", 
                                        type_regle=TypeRegleMetierEnum.VALEUR,
                                        valeur_defaut=10)

        return count >= seuil
    
    def lot_existant_pour(self, enveloppe: Enveloppe) -> Optional[LotExpediteur]:
        """
        Vérifie si l'expéditeur est déjà associé à un lot existant ouvert.
        """
        expediteur = enveloppe.expediteur

        if not expediteur:
            return None
        
        return self.session.query(LotExpediteur).filter(
            LotExpediteur.expediteur_id == expediteur.id,
            LotExpediteur.statut == StatutLotExpediteurEnum.OUVERT,
            LotExpediteur.site_id == enveloppe.site_id
        ).first()
    
    def attribuer_casier_lot(self, lot : LotExpediteur):
         # Trouver un casier disponible
        lot.casier = ServiceCasier.obtenir_casier_disponible(lot.site)

        if lot.casier is None:
            raise HTTPException(status_code=400, detail=f"Impossible de trouver un casier disponible pour cet expéditeur sur le site {lot.site.nom}")

        # Spécifier que ce Casier est désormais rattaché à ce Lot
        lot.casier.lot_expediteur_id = lot.id

        self.session.commit()
        
    
    def creer_lot_expediteur(self, enveloppe: Enveloppe) -> LotExpediteur:
        """Crée un lot pour un expéditeur avec une première enveloppe frauduleuse."""
        lot = LotExpediteur(
            expediteur_id=enveloppe.expediteur_id,
            statut=StatutLotExpediteurEnum.OUVERT,
            date_creation=datetime.utcnow(),
            site=enveloppe.site
        )
        
        self.session.add(lot)       

        self.attribuer_casier_lot(lot)

        return lot
    
    def changer_statut_lot(self, lot_id: int, statut: StatutLotExpediteurEnum) -> Optional[LotExpediteur]:
        """Change le statut d'un lot."""
        lot = self.session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
        if not lot:
            return None
            
        lot.statut = statut
        self.session.commit()
        return lot
        
    def appliquer_option_recouvrement(
        self, 
        lot_id: int, 
        option: OptionTraitementLotExpediteurEnum,
        adresse_renvoi: Optional[str] = None
    ) -> Optional[LotExpediteur]:
        """Applique l'option de recouvrement choisie par l'expéditeur."""
        lot = self.session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
        if not lot:
            return None
        
        lot.option_recouvrement = option
        
        if option == OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR and adresse_renvoi:
            lot.adresse_renvoi = adresse_renvoi
        
        self.session.commit()
        return lot
    
    def enregistrer_paiement(self, lot_id: int) -> Optional[LotExpediteur]:
        """Enregistre le paiement d'un lot et met à jour son statut."""
        lot = self.session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
        if not lot:
            return None
        
        lot.statut = StatutLotExpediteurEnum.PAIEMENT_RECU
        lot.date_paiement = datetime.utcnow()
        
        self.session.commit()
        return lot
    
    def traiter_lot_apres_paiement(self, lot_id: int) -> Optional[LotExpediteur]:
        """
        Traite les enveloppes d'un lot après paiement selon l'option choisie.
        """
        lot = self.session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
        if not lot or lot.statut != StatutLotExpediteurEnum.PAIEMENT_RECU:
            return None
        
        # Mapping des options de traitement vers les statuts d'enveloppe
        statut_mapping = {
            OptionTraitementLotExpediteurEnum.LIVRAISON_DESTINATAIRE: StatutEnveloppeEnum.EN_COURS,
            OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE: StatutEnveloppeEnum.A_RECUPERER,
            OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR: StatutEnveloppeEnum.A_RENVOYER
        }
        
        # Traiter les enveloppes selon l'option choisie
        for enveloppe in lot.enveloppes:
            enveloppe.statut = statut_mapping.get(lot.option_recouvrement)
        
        lot.statut = StatutLotExpediteurEnum.TRAITE
        
        # Libérer le casier sauf pour l'option récupération sur site
        if lot.option_recouvrement != OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE:
            self.liberer_casiers(lot)
        
        self.session.commit()
        return lot
    
    def recuperer_lot_sur_site(self, lot_id: int) -> Optional[LotExpediteur]:
        """
        Marque un lot comme récupéré par l'expéditeur et libère le casier.
        """
        lot = self.session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
        if not lot or lot.statut != StatutLotExpediteurEnum.TRAITE:
            return None
        
        # Vérifier que l'option choisie est bien la récupération sur site
        if lot.option_recouvrement != OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE:
            return None
        
        # Marquer les enveloppes comme récupérées
        for enveloppe in lot.enveloppes:
            enveloppe.statut = StatutEnveloppeEnum.RECUPEREE
        
        # Libérer les casiers
        self.liberer_casiers(lot)
        
        lot.date_recuperation = datetime.utcnow()
        self.session.commit()
        return lot
        
    def liberer_casiers(self, lot: LotExpediteur) -> None:
        """Libère tous les casiers associés à un lot."""

        # Relacher l'ensemble des Casier associées        
        for casier in lot.casiers:
            casier.lot_expediteur_id = None

        self.session.commit()

        # Relâcher le casier
        if lot.casier:
            lot.casier_id = None

        # Pour toutes les enveloppes du Lot
        for enveloppe in lot.enveloppes:
            # Changer le statut de l'enveloppe
            enveloppe.casier_id = None

    @classmethod
    def calcul_somme_valorisation(cls, lot):
        # Initialisation des compteurs
        cumul = {
            'postage': {
                'cout_enveloppe': 0,
                'cout_affranchissements_valide': 0,
                'nb_affranchissements_invalides': 0,
                'montant_sous_affranchissement': 0,
                'presence_affranchissements_invalide': False,
                'presence_taxe': False,
            },
            'livraison': {
                'taxe_livraison_a_recuperer': 0,
                'taxe_livraison_fixe': 0,
                'taxe_livraison_totale': 0,
                'cout_total': 0
            },
            'collecte': {
                'cout_ht': 0,
                'cout_tva': 0,
                'cout_ttc': 0
            },
            'expédition': {
                'cout_ht': 0,
                'cout_tva': 0,
                'cout_ttc': 0
            }
        }
        
        # Parcourir toutes les enveloppes du lot
        for enveloppe in lot.enveloppes:
            if not enveloppe.valorisation:
                continue
                
            # Cumul des valeurs de postage
            if 'postage' in enveloppe.valorisation:
                postage = enveloppe.valorisation['postage']
                cumul['postage']['cout_enveloppe'] += postage.get('cout_enveloppe', 0)
                cumul['postage']['cout_affranchissements_valide'] += postage.get('cout_affranchissements_valide', 0)
                cumul['postage']['nb_affranchissements_invalides'] += postage.get('nb_affranchissements_invalides', 0)
                cumul['postage']['montant_sous_affranchissement'] += postage.get('montant_sous_affranchissement', 0)
                cumul['postage']['presence_affranchissements_invalide'] |= postage.get('presence_affranchissements_invalide', False)
                cumul['postage']['presence_taxe'] |= postage.get('presence_taxe', False)
            
            # Cumul des valeurs de livraison
            if 'livraison' in enveloppe.valorisation:
                livraison = enveloppe.valorisation['livraison']
                cumul['livraison']['taxe_livraison_a_recuperer'] += livraison.get('taxe_livraison_a_recuperer', 0)
                cumul['livraison']['taxe_livraison_fixe'] += livraison.get('taxe_livraison_fixe', 0)
                cumul['livraison']['taxe_livraison_totale'] += livraison.get('taxe_livraison_totale', 0)
                cumul['livraison']['cout_total'] += livraison.get('cout_total', 0)
            
            # Cumul des valeurs de collecte
            if 'collecte' in enveloppe.valorisation:
                collecte = enveloppe.valorisation['collecte']
                cumul['collecte']['cout_ht'] += collecte.get('cout_ht', 0)
                cumul['collecte']['cout_tva'] += collecte.get('cout_tva', 0)
                cumul['collecte']['cout_ttc'] += collecte.get('cout_ttc', 0)
            
            # Cumul des valeurs d'expédition
            if 'expédition' in enveloppe.valorisation:
                expedition = enveloppe.valorisation['expédition']
                cumul['expédition']['cout_ht'] += expedition.get('cout_ht', 0)
                cumul['expédition']['cout_tva'] += expedition.get('cout_tva', 0)
                cumul['expédition']['cout_ttc'] += expedition.get('cout_ttc', 0)
        
        return cumul

    def reassigner_enveloppe_apres_changement_expediteur(self, enveloppe: Enveloppe, ancien_expediteur_id: int):
        """
        Réassigne une enveloppe à un nouveau lot expéditeur après changement d'expéditeur.
        """
        # Si l'enveloppe n'est pas dans un lot, rien à faire
        # if not enveloppe.lot_expediteur_id:
        #     return
        
        # Retirer l'enveloppe de son lot actuel
        ancien_lot = enveloppe.lot_expediteur
        enveloppe.lot_expediteur_id = None
        enveloppe.casier_id = None
        self.session.commit()
        
        # Vérifier si l'ancien lot est vide et le traiter si nécessaire
        if ancien_lot:
            nb_enveloppes = self.session.query(func.count(Enveloppe.id)).filter(
                Enveloppe.lot_expediteur_id == ancien_lot.id
            ).scalar()
            
            if nb_enveloppes == 0 and ancien_lot.statut == StatutLotExpediteurEnum.OUVERT:
                # Si le lot est vide et ouvert, on peut le supprimer
                self.session.delete(ancien_lot)
                self.session.commit()
        
        # Réassigner l'enveloppe à un nouveau lot si nécessaire
        if enveloppe.expediteur_id:
            self.lot_expediteur(enveloppe)
