from typing import List, Optional
from sqlmodel import SQLModel
from constants.enumerations import StatutCasierEnum, StatutLotExpediteurEnum, FrontendActionEnum, NatureAffranchissementEnum, CategorieAffranchissementEnum, TypeAffranchissementEnum, DeviseEnum, StatutVerificationEnum, SousTypeAffranchissementEnum, StatutEnveloppeEnum, ProduitEnum, ValiditeAffranchissementEnum, DestinationEnveloppeEnum
from models.business import Casier, LotExpediteur, Affranchissement, VerificationAffranchissement, PhotoEnveloppe, Site, Expediteur, Destination
from pydantic import ConfigDict, model_validator, computed_field
from datetime import datetime
from models.users import User
from services.api_laposte.produits_laposte import recuperer_produit_par_code_produit
from pydantic import Field


class AjoutAffranchissement(SQLModel):
    categorie: CategorieAffranchissementEnum | None = None
    type: TypeAffranchissementEnum | None = None
    prix_unite_devise: float | None = None
    devise: DeviseEnum | None = None
    code: str | None = None
    nature: NatureAffranchissementEnum | None = None
    quantite: int | None = None
    statut: ValiditeAffranchissementEnum | None = None
    origine: str | None = None


class ModificationAffranchissement(SQLModel):
    nature: NatureAffranchissementEnum | None = None
    quantite: int | None = None
    statut: ValiditeAffranchissementEnum | None = None
    prix_unite_devise: float | None = None

class ValeurModeleAffranchissement:
    """
    Option d'un produit postal
    """
    def __init__(self, prix_unite_devise: float = None, 
                 color: str = None, 
                 variant: str = None, 
                 devise: DeviseEnum = None,
                 code_produit: str = None):
        self.prix_unite_devise = prix_unite_devise
        self.color = color
        self.variant = variant
        self.devise = devise
        self.code_produit = code_produit

class ModeleAffranchissement(SQLModel):
    """
    Produit postal
    """
    categorie: Optional[CategorieAffranchissementEnum] = None
    type: TypeAffranchissementEnum
    color: Optional[str] = None
    variant: Optional[str] = None
    devise: Optional[DeviseEnum] = None
    prix_unite_devise: Optional[float] = None
    origine: Optional[str] = None
    code_produit: Optional[str] = None
    produit_postal: Optional[dict] = Field(None, exclude=True)
    valorisation_complexe: Optional[bool] = False
    afficher: Optional[bool] = True

    class Config:
        from_attributes = True  # Pour permettre la sérialisation des attributs
        populate_by_name = True

    def __new__(cls,
                type: TypeAffranchissementEnum, 
                color: str = None, 
                variant: str = None,
                devise: DeviseEnum = None, 
                prix_unite_devise: float = None,
                origine: str = None,
                valeurs: List[ValeurModeleAffranchissement] = [],
                categorie: CategorieAffranchissementEnum = None,
                code_produit: str = None,
                valorisation_complexe: bool = None,
                afficher: bool = True
                ):

        construction_params = {
            "categorie": categorie,
            "type": type,
            "color": color,
            "variant": variant,
            "devise": devise,
            "prix_unite_devise": prix_unite_devise,
            "origine": origine,
            "code_produit": code_produit,
            "valorisation_complexe": valorisation_complexe,
            "afficher": afficher
        }

        if len(valeurs) > 0:
            elements = []

            for valeur in valeurs:
                params = construction_params.copy()
                for attr in ['devise', 'prix_unite_devise', 'color', 'variant', 'code_produit']:
                    if getattr(valeur, attr) is not None:
                        params[attr] = getattr(valeur, attr)

                instance = super().__new__(cls)
                for key, value in params.items():
                    setattr(instance, key, value)
                elements.append(instance)

            return elements

        else:
            instance = super().__new__(cls)
            for key, value in construction_params.items():
                setattr(instance, key, value)
            return instance
    
    def __init__(self, **data):
        super().__init__(**data)

        self.produit_postal = recuperer_produit_par_code_produit(self.code_produit) if self.code_produit else None

        if self.produit_postal:
            _price = self.produit_postal.get("basePrice", None)
            self.devise = {
                "EUR": DeviseEnum.EURO,
                "FRA": DeviseEnum.FRANCS,
                "A": DeviseEnum.ANCIEN_FRANCS
            }.get(_price.get("currencyIso"))
            self.prix_unite_devise = _price.get("value")

    @property
    def valorisable(self) -> bool:
        #TODO: revoir
        return self.devise is not None and self.code_produit is None and self.prix_unite_devise is None and self.categorie != CategorieAffranchissementEnum.CODE
    
    @property
    def champs_requis(self) -> List[str]:
        if self.valorisable:
            return ["prix_unite_devise"]
        
        if self.prix_unite_devise is None and self.categorie == CategorieAffranchissementEnum.CODE:
            return ["nature"]

        return []
    
    @computed_field
    @property
    def informations(self) -> dict:
        return {
            "champs_requis": self.champs_requis,
            "complet": len(self.champs_requis) == 0,
            "label": f"{self.type}{self.origine if self.origine else ''}"
        }

        
class CategorieAffranchissement(SQLModel):
    """
    Gamme de Produit Postaux
    """
    id: CategorieAffranchissementEnum
    display1: str
    display2: str
    description: str
    color: str
    variant: str
    types_affranchissements: List[ModeleAffranchissement] = []

    def __init__(self, **data):
        types_affranchissements = data.pop("types_affranchissements")
        
        data["types_affranchissements"] = []
        for type_aff in types_affranchissements:
            if isinstance(type_aff, list):
                data["types_affranchissements"].extend(type_aff)
            else:
                data["types_affranchissements"].append(type_aff)
                
        # Set categories for each
        for type_aff in data["types_affranchissements"]:
            type_aff.categorie = data["id"]

        super().__init__(**data)

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True



class AffranchissementItemPublic(SQLModel):
    id: int | None
    id_migration: str | None
    categorie: CategorieAffranchissementEnum
    type: TypeAffranchissementEnum
    sous_type: SousTypeAffranchissementEnum
    nature: NatureAffranchissementEnum | None = None
    prix_unite_devise: float | None
    devise: DeviseEnum
    quantite: int
    poids_max: float
    code: str | None = None
    statut: StatutVerificationEnum = StatutVerificationEnum.VALIDE
    prix_unite_euros: float | None = None
    verifications: list[VerificationAffranchissement]
    origine: str | None = None

    # Champs calculés
    
    informations: dict
    donnees: dict | None = None

class UserPublic(SQLModel):
    email: str
    id: int

class EnveloppeItemPublic(SQLModel):
    id: int
    statut: str
    affranchissements: List[AffranchissementItemPublic]
    photos: List[PhotoEnveloppe]
    valorisation: dict | None = None
    site: Site | None = None
    expediteur: Expediteur | None = None
    destination: Destination | None = None
    casier: Casier | None = None
    poids: float
    surpoids: bool
    surdimensionne: bool
    destination_enveloppe: DestinationEnveloppeEnum
    updated_at: datetime
    created_at: datetime
    completed_at: datetime | None = None
    user: UserPublic | None = None

    # Champs calculés
    informations: dict | None = None


class EnveloppePublic(SQLModel):
    enveloppe: EnveloppeItemPublic
    affranchissement: AffranchissementItemPublic | None = None
    frontend_action: FrontendActionEnum

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )

class CreationEnveloppe(SQLModel):
    id: int | None = None

    poids: float | None = None
    surpoids: bool | None = None
    surdimensionne: bool | None = None
    destination_enveloppe: DestinationEnveloppeEnum = DestinationEnveloppeEnum.METROPOLE
    site_id: int | None = None
    
    # optionnels
    destination_id: int | None = None
    expediteur_id: int | None = None
    

from pydantic import BaseModel
from typing import List, TypeVar, Generic

T = TypeVar('T')

class PaginatedResponse(BaseModel, Generic[T]):
    total_pages: int
    current_page: int
    page_size: int
    total_items: int
    items: List[T]

class LotExpediteurPublic(SQLModel):
    id: int | None = None

    statut: StatutLotExpediteurEnum

    casier: Casier | None = None
    casiers: List[Casier] = []
    expediteur: Expediteur
    site: Site

    nombre_enveloppes: int
    valorisation: dict = {}

    created_at: datetime

class LotExpediteurDetails(SQLModel):
    id: int | None = None
    statut: StatutLotExpediteurEnum
    
    casier: Casier | None = None
    casiers: List[Casier] = []
    expediteur: Expediteur
    site: Site
    
    nombre_enveloppes: int
    valorisation: dict = {}
    
    created_at: datetime
    
    # Detailed information
    enveloppes: List[EnveloppeItemPublic] = []  

class CasierPublic(SQLModel):
    id: int | None = None
    numero: str
    emplacement: str
    site: Site
    site_id: int
    statut: StatutCasierEnum
    lot_expediteur: LotExpediteurPublic | None = None


class RechercheExpediteur(SQLModel):
    nom: Optional[str] = None
    adresse: Optional[str] = None
    ville: Optional[str] = None
    code_postal: Optional[str] = None
    