import pytest
from sqlmodel import Session
from models.business import LotExpediteur, Enveloppe, Affranchissement, Casier, Site, Expediteur
from tests.conftest import client as _client
from io import StringIO
import csv
from constants.enumerations import DestinationEnveloppeEnum, CategorieAffranchissementEnum, DeviseEnum, TypeAffranchissementEnum, StatutLotExpediteurEnum
from tests.fixtures.affranchissements import S10

class TestLotsExpediteursAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/lots-expediteurs/")
        self.super_client = _client(admin_user, "/lots-expediteurs/")

    def test_get_lot_expediteur_csv(self, db: Session, site : Site, user) -> None:
        """Test la génération du CSV pour un lot expéditeur"""
        # Créer un lot expéditeur avec des enveloppes et des affranchissements
        casier = Casier(site_id=site.id, numero="1", capacite_max=100)

        lot = LotExpediteur(nom="Lot Test CSV")
        lot.casier = casier
        db.add(lot)
        db.commit()
        db.refresh(lot)

        expediteur = Expediteur(nom="Dupont Marc")
        
        # Créer des enveloppes avec des affranchissements
        enveloppe1 = Enveloppe(lot_expediteur_id=lot.id, expediteur=expediteur, site_id=site.id, casier_id=casier.id, destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, user_id=user.id)
        enveloppe2 = Enveloppe(lot_expediteur_id=lot.id, site_id=site.id, casier_id=casier.id, destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, user_id=user.id)
        db.add(expediteur)
        db.add(enveloppe1)
        db.add(enveloppe2)
        db.commit()
        
        # Ajouter des affranchissements
        affranchissement1 = S10(code="CODE123", enveloppe_id=enveloppe1.id, user_id=user.id)
        affranchissement2 = S10(code="CODE456", enveloppe_id=enveloppe1.id, user_id=user.id)
        affranchissement3 = S10(code="CODE789", enveloppe_id=enveloppe2.id, user_id=user.id)
        affranchissement4 = Affranchissement(
                                categorie=CategorieAffranchissementEnum.BEAU,
                                type=TypeAffranchissementEnum.ECO20G, 
                                devise=DeviseEnum.EURO,
                                enveloppe_id=enveloppe2.id,
                                user_id=user.id
                            )
        db.add(affranchissement1)
        db.add(affranchissement2)
        db.add(affranchissement3)
        db.add(affranchissement4)
        db.commit()
        
        # Appeler l'API pour obtenir le CSV
        response = self.client.get(f"/{lot.id}/csv")
        assert response.status_code == 403

        response = self.super_client.get(f"/{lot.id}/csv")     
        assert response.status_code == 200
        assert response.headers["Content-Type"] == "text/csv; charset=utf-8"
        assert response.headers["Content-Disposition"] == f"attachment; filename=lot_expediteur_{lot.id}.csv"
        
        # Vérifier le contenu du CSV
        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)
        
        # Vérifier l'en-tête
        assert rows[0] == ["Site", "Casier", "Enveloppe ID", "Produit", "Expéditeur", "Codes d'affranchissement", "Valorisation Cas A", "Valorisation Cas C"]
        
        # Vérifier les données
        assert len(rows) == 4  # En-tête + 2 enveloppes + Footer

        assert str(enveloppe1.id) in rows[1] == [site.nom, "1", str(enveloppe1.id), "METROPOLE", "Dupont Marc", "CODE123, CODE456", "0.00", "0.00"]
        assert str(enveloppe2.id) in rows[2] == [site.nom, "1", str(enveloppe2.id), "METROPOLE", "INCONNU", "CODE789", "0.00", "0.00"]
        assert rows[3] == ["Total", "", "", "", "", "0.00", "0.00"]

    def test_terminer_lot_expediteur(self, db: Session, site: Site, user) -> None:
        """Test la terminaison d'un lot expéditeur"""
        # Créer un casier
        casier = Casier(site_id=site.id, numero="2", capacite_max=100)
        db.add(casier)
        db.commit()
        db.refresh(casier)
        
        # Créer un lot expéditeur avec un casier
        lot = LotExpediteur(nom="Lot Test Terminer", casier=casier)
        db.add(lot)
        db.commit()
        db.refresh(lot)
        
        # Créer des enveloppes associées au lot et au casier
        expediteur = Expediteur(nom="Martin Dupont")
        db.add(expediteur)
        db.commit()
        
        enveloppe1 = Enveloppe(lot_expediteur_id=lot.id, expediteur=expediteur, site_id=site.id, 
                               casier_id=casier.id, destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, user_id=user.id)
        enveloppe2 = Enveloppe(lot_expediteur_id=lot.id, site_id=site.id, 
                               casier_id=casier.id, destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, user_id=user.id)
        db.add(enveloppe1)
        db.add(enveloppe2)
        db.commit()
        
        # Appeler l'API pour terminer le lot
        response = self.client.post(f"/{lot.id}/terminer")
        assert response.status_code == 403

        response = self.super_client.post(f"/{lot.id}/terminer")
        
        # Vérifier le statut de la réponse
        assert response.status_code == 200
        
        # Vérifier que le lot est bien terminé
        lot_termine = response.json()
        assert lot_termine["statut"] == StatutLotExpediteurEnum.TRAITE.value
        
        # Vérifier que le casier a été relâché
        assert lot_termine["casier_id"] is None
        
        # Vérifier que les enveloppes n'ont plus de casier
        db.refresh(enveloppe1)
        db.refresh(enveloppe2)
        assert enveloppe1.casier_id is None
        assert enveloppe2.casier_id is None

    def test_get_lots_expediteurs(self, db: Session, site: Site, user, expediteur) -> None:
        """Test la récupération paginée des lots expéditeurs"""
        # Créer plusieurs lots expéditeurs
        casier = Casier(site_id=site.id, numero="3", capacite_max=100)
        db.add(casier)
        db.commit()
        
        lots = []
        for i in range(3):
            lot = LotExpediteur(nom=f"Lot Test {i}", expediteur=expediteur, site=site)
            lot.casier = casier
            db.add(lot)
            db.commit()
            db.refresh(lot)
            lots.append(lot)
        
        # Appeler l'API pour obtenir la liste paginée
        response = self.super_client.get("/")
        assert response.status_code == 200, response.json()
        
        # Vérifier la structure de la réponse paginée
        data = response.json()
        assert "items" in data
        assert "total_pages" in data
        assert "current_page" in data
        assert "page_size" in data
        assert "total_items" in data
        
        # Vérifier que les lots créés sont présents
        lot_ids = [item["id"] for item in data["items"]]
        for lot in lots:
            assert lot.id in lot_ids

    def test_update_statut_lot_expediteur(self, db: Session, site: Site, user) -> None:
        """Test la mise à jour du statut d'un lot expéditeur"""
        # Créer un casier
        casier = Casier(site_id=site.id, numero="4", capacite_max=100)
        db.add(casier)
        db.commit()
        
        # Créer un lot expéditeur
        lot = LotExpediteur(nom="Lot Test Statut", casier=casier)
        db.add(lot)
        db.commit()
        db.refresh(lot)
        
        # Créer des enveloppes associées au lot
        enveloppe = Enveloppe(lot_expediteur_id=lot.id, site_id=site.id, 
                             casier_id=casier.id, destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, user_id=user.id)
        db.add(enveloppe)
        db.commit()
        
        # Appeler l'API pour mettre à jour le statut
        response = self.client.patch(f"/{lot.id}/statut?statut={StatutLotExpediteurEnum.TRAITE.value}")
        assert response.status_code == 403, response.json()
        
        response = self.super_client.patch(f"/{lot.id}/statut?statut={StatutLotExpediteurEnum.TRAITE.value}")
        assert response.status_code == 200, response.json()
        
        # Vérifier que le statut a été mis à jour
        lot_updated = response.json()
        assert lot_updated["statut"] == StatutLotExpediteurEnum.TRAITE.value
        
        # Vérifier que le casier a été relâché
        assert lot_updated["casier_id"] is None
        
        # Vérifier que l'enveloppe n'a plus de casier
        db.refresh(enveloppe)
        assert enveloppe.casier_id is None

    def test_get_lot_expediteur_valorisation(self, db: Session, site: Site, user, expediteur) -> None:
        """Test la récupération de la valorisation d'un lot expéditeur"""
        casier = Casier(site_id=site.id, numero="3", capacite_max=100)
        db.add(casier)
        db.commit()
        
        # Créer un lot expéditeur
        lot = LotExpediteur(nom="Lot Test Valorisation", site=site, expediteur=expediteur, casier=casier)
        db.add(lot)
        db.commit()
        db.refresh(lot)
        
        # Créer des enveloppes avec valorisation
        enveloppe1 = Enveloppe(
            lot_expediteur_id=lot.id, 
            site_id=site.id, 
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, 
            user_id=user.id,
            valorisation={
                "postage": {
                    "cout_enveloppe": 1.5,
                    "cout_affranchissements_valide": 2.0,
                    "nb_affranchissements_invalides": 0,
                    "montant_sous_affranchissement": 0,
                    "presence_affranchissements_invalide": False,
                    "presence_taxe": False
                },
                "livraison": {
                    "taxe_livraison_a_recuperer": 1.0,
                    "taxe_livraison_fixe": 0.5,
                    "taxe_livraison_totale": 1.5,
                    "cout_total": 3.0
                },
                "collecte": {
                    "cout_ht": 2.0,
                    "cout_tva": 0.4,
                    "cout_ttc": 2.4
                },
                "expédition": {
                    "cout_ht": 3.0,
                    "cout_tva": 0.6,
                    "cout_ttc": 3.6
                }
            }
        )
        
        enveloppe2 = Enveloppe(
            lot_expediteur_id=lot.id, 
            site_id=site.id, 
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, 
            user_id=user.id,
            valorisation={
                "postage": {
                    "cout_enveloppe": 1.0,
                    "cout_affranchissements_valide": 1.5,
                    "nb_affranchissements_invalides": 1,
                    "montant_sous_affranchissement": 0.5,
                    "presence_affranchissements_invalide": True,
                    "presence_taxe": True
                },
                "livraison": {
                    "taxe_livraison_a_recuperer": 0.5,
                    "taxe_livraison_fixe": 0.5,
                    "taxe_livraison_totale": 1.0,
                    "cout_total": 2.0
                },
                "collecte": {
                    "cout_ht": 1.0,
                    "cout_tva": 0.2,
                    "cout_ttc": 1.2
                },
                "expédition": {
                    "cout_ht": 2.0,
                    "cout_tva": 0.4,
                    "cout_ttc": 2.4
                }
            }
        )
        
        db.add(enveloppe1)
        db.add(enveloppe2)
        db.commit()
        
        # Appeler l'API pour obtenir la valorisation        
        response = self.super_client.get(f"/")
        assert response.status_code == 200, response.json()
        
        # Vérifier la structure et les valeurs de la valorisation
        lots = response.json()

        lot = lots["items"][0]
        valorisation = lot["valorisation"]
        
        
        # Vérifier les cumuls
        assert valorisation["postage"]["cout_enveloppe"] == 2.5
        assert valorisation["postage"]["cout_affranchissements_valide"] == 3.5
        assert valorisation["postage"]["nb_affranchissements_invalides"] == 1
        assert valorisation["postage"]["montant_sous_affranchissement"] == 0.5
        assert valorisation["postage"]["presence_affranchissements_invalide"] == True
        assert valorisation["postage"]["presence_taxe"] == True
        
        assert valorisation["livraison"]["taxe_livraison_a_recuperer"] == 1.5
        assert valorisation["livraison"]["taxe_livraison_fixe"] == 1.0
        assert valorisation["livraison"]["taxe_livraison_totale"] == 2.5
        assert valorisation["livraison"]["cout_total"] == 5.0
        
        # Utiliser round pour gérer les erreurs de précision des nombres à virgule flottante
        assert round(valorisation["collecte"]["cout_ht"], 1) == 3.0
        assert round(valorisation["collecte"]["cout_tva"], 1) == 0.6
        assert round(valorisation["collecte"]["cout_ttc"], 1) == 3.6
        
        assert round(valorisation["expédition"]["cout_ht"], 1) == 5.0
        assert round(valorisation["expédition"]["cout_tva"], 1) == 1.0
        assert round(valorisation["expédition"]["cout_ttc"], 1) == 6.0
        
    def test_update_statut_lot_expediteur_annule(self, db: Session, site: Site, user) -> None:
        """Test la mise à jour du statut d'un lot expéditeur à ANNULE"""
        # Créer un casier
        casier = Casier(site_id=site.id, numero="5", capacite_max=100)
        db.add(casier)
        db.commit()
        
        # Créer un lot expéditeur
        lot = LotExpediteur(nom="Lot Test Annulation", casier=casier)
        db.add(lot)
        db.commit()
        db.refresh(lot)
        
        # Créer des enveloppes associées au lot
        enveloppe = Enveloppe(lot_expediteur_id=lot.id, site_id=site.id, 
                             casier_id=casier.id, destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, user_id=user.id)
        db.add(enveloppe)
        db.commit()
        
        # Appeler l'API pour mettre à jour le statut à ANNULE
        response = self.client.patch(f"/{lot.id}/statut?statut={StatutLotExpediteurEnum.ANNULE.value}")
        assert response.status_code == 403, response.json()
        
        response = self.super_client.patch(f"/{lot.id}/statut?statut={StatutLotExpediteurEnum.ANNULE.value}")
        assert response.status_code == 200, response.json()
        
        # Vérifier que le statut a été mis à jour
        lot_updated = response.json()
        assert lot_updated["statut"] == StatutLotExpediteurEnum.ANNULE.value
        
        # Vérifier que le casier a été relâché
        assert lot_updated["casier_id"] is None
        
        # Vérifier que l'enveloppe n'a plus de casier
        db.refresh(enveloppe)
        assert enveloppe.casier_id is None

        # Vérifier qu'aucun Casier n'est associé à ce LotExpediteur
        casier = db.query(Casier).filter_by(lot_expediteur_id=lot.id).all()
        assert casier == []

    def test_attribuer_nouveau_casier(self, db: Session, site: Site, user) -> None:
        """Test l'attribution d'un nouveau casier à un lot expéditeur"""
        # Créer un casier initial
        casier_initial = Casier(site_id=site.id, numero="10", capacite_max=100)
        db.add(casier_initial)
        db.commit()
        db.refresh(casier_initial)
        
        # Créer un lot expéditeur avec le casier initial
        lot = LotExpediteur(nom="Lot Test Nouveau Casier", 
                            casier=casier_initial, 
                            statut=StatutLotExpediteurEnum.OUVERT,
                            site=site)
        db.add(lot)
        db.commit()
        db.refresh(lot)
        
        # Créer une enveloppe associée au lot et au casier initial
        enveloppe = Enveloppe(
            lot_expediteur_id=lot.id, 
            site_id=site.id, 
            casier_id=casier_initial.id, 
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, 
            user_id=user.id
        )
        db.add(enveloppe)
        db.commit()
        
        # Créer un nouveau casier disponible
        nouveau_casier = Casier(site_id=site.id, numero="11", capacite_max=100)
        db.add(nouveau_casier)
        db.commit()
        db.refresh(nouveau_casier)
        
        # Vérifier l'état initial
        assert lot.casier_id == casier_initial.id
        
        # Appeler l'API pour attribuer un nouveau casier
        response = self.client.post(f"/{lot.id}/nouveau-casier")
        assert response.status_code == 403, response.json()
        
        response = self.super_client.post(f"/{lot.id}/nouveau-casier")
        assert response.status_code == 200, response.json()
        
        # Vérifier que le casier a été mis à jour
        lot_updated = response.json()
        assert lot_updated["casier_id"] != casier_initial.id
        assert lot_updated["casier_id"] == nouveau_casier.id
        
        # Vérifier en base de données
        db.refresh(lot)
        assert lot.casier_id == nouveau_casier.id
        
        # Vérifier que l'enveloppe est toujours associée à l'ancien casier
        db.refresh(enveloppe)
        assert enveloppe.casier_id == casier_initial.id
        
        # Créer une nouvelle enveloppe pour vérifier qu'elle est associée au nouveau casier
        nouvelle_enveloppe = Enveloppe(
            lot_expediteur_id=lot.id, 
            site_id=site.id, 
            casier_id=lot.casier_id,  # Utiliser le nouveau casier
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, 
            user_id=user.id
        )
        db.add(nouvelle_enveloppe)
        db.commit()
        db.refresh(nouvelle_enveloppe)
        
        # Vérifier que la nouvelle enveloppe est bien associée au nouveau casier
        assert nouvelle_enveloppe.casier_id == nouveau_casier.id

    def test_pagination_avec_tri_sous_affranchissement(self, db: Session, site: Site, user, expediteur) -> None:
        """Test la pagination avec tri par sous-affranchissement"""
        # Créer un casier
        casier = Casier(site_id=site.id, numero="6", capacite_max=100)
        db.add(casier)
        db.commit()
        
        # Créer plusieurs lots expéditeurs avec différentes valorisations
        lots = []
        for i in range(15):  # Créer 15 lots pour avoir plusieurs pages
            lot = LotExpediteur(nom=f"Lot Test Tri {i}", expediteur=expediteur, site=site, casier=casier)
            db.add(lot)
            db.commit()
            db.refresh(lot)
            
            # Créer une enveloppe avec une valorisation spécifique
            # Montant de sous-affranchissement décroissant (15, 14, 13, ...)
            enveloppe = Enveloppe(
                lot_expediteur_id=lot.id, 
                site_id=site.id, 
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE, 
                user_id=user.id,
                valorisation={
                    "postage": {
                        "cout_enveloppe": 1.0,
                        "cout_affranchissements_valide": 1.0,
                        "nb_affranchissements_invalides": 0,
                        "montant_sous_affranchissement": 15 - i,  # Valeurs décroissantes
                        "presence_affranchissements_invalide": False,
                        "presence_taxe": False
                    },
                    "livraison": {
                        "taxe_livraison_a_recuperer": 0.0,
                        "taxe_livraison_fixe": 0.0,
                        "taxe_livraison_totale": 0.0,
                        "cout_total": 0.0
                    },
                    "collecte": {
                        "cout_ht": 0.0,
                        "cout_tva": 0.0,
                        "cout_ttc": 0.0
                    },
                    "expédition": {
                        "cout_ht": 0.0,
                        "cout_tva": 0.0,
                        "cout_ttc": 0.0
                    }
                }
            )
            db.add(enveloppe)
            db.commit()
            lots.append(lot)
        
        # Appeler l'API pour obtenir la première page avec tri par sous-affranchissement décroissant
        response = self.super_client.get("/?sort_by=sous_affranchissement&sort_order=desc&page=1&page_size=5")
        assert response.status_code == 200
        
        # Vérifier la structure de la réponse paginée
        data_page1 = response.json()
        assert "items" in data_page1
        assert "total_pages" in data_page1
        assert "current_page" in data_page1
        assert data_page1["current_page"] == 1
        assert data_page1["page_size"] == 5
        assert data_page1["total_items"] == 15
        assert len(data_page1["items"]) == 5
        
        # Vérifier que les lots sont triés par montant de sous-affranchissement décroissant
        # Les 5 premiers lots devraient avoir les montants 15, 14, 13, 12, 11
        sous_aff_page1 = [item["valorisation"]["postage"]["montant_sous_affranchissement"] for item in data_page1["items"]]
        assert sous_aff_page1 == [15.0, 14.0, 13.0, 12.0, 11.0]
        
        # Appeler l'API pour obtenir la deuxième page
        response = self.super_client.get("/?sort_by=sous_affranchissement&sort_order=desc&page=2&page_size=5")
        assert response.status_code == 200
        
        # Vérifier la structure de la réponse paginée
        data_page2 = response.json()
        assert data_page2["current_page"] == 2
        assert len(data_page2["items"]) == 5
        
        # Vérifier que les lots sont triés par montant de sous-affranchissement décroissant
        # Les 5 lots suivants devraient avoir les montants 10, 9, 8, 7, 6
        sous_aff_page2 = [item["valorisation"]["postage"]["montant_sous_affranchissement"] for item in data_page2["items"]]
        assert sous_aff_page2 == [10.0, 9.0, 8.0, 7.0, 6.0]
        
        # Appeler l'API pour obtenir la troisième page
        response = self.super_client.get("/?sort_by=sous_affranchissement&sort_order=desc&page=3&page_size=5")
        assert response.status_code == 200
        
        # Vérifier la structure de la réponse paginée
        data_page3 = response.json()
        assert data_page3["current_page"] == 3
        assert len(data_page3["items"]) == 5
        
        # Vérifier que les lots sont triés par montant de sous-affranchissement décroissant
        # Les 5 derniers lots devraient avoir les montants 5, 4, 3, 2, 1
        sous_aff_page3 = [item["valorisation"]["postage"]["montant_sous_affranchissement"] for item in data_page3["items"]]
        assert sous_aff_page3 == [5.0, 4.0, 3.0, 2.0, 1.0]
