import pytest
from fastapi.testclient import TestClient
from core.config import settings
from main import app
from tests.utils.user import get_token_for_user
from sqlmodel import SQLModel, Session
from models import User, UserCreate
from sqlalchemy import create_engine, text
from sqlalchemy_utils import create_database, database_exists, drop_database
import os
import subprocess
import glob
from alembic.config import Config
from alembic import command

@pytest.fixture(scope="session")
def db_engine():
    """
    Fixture to create and provide a test database engine.
    """
    # Utilisez un identifiant unique pour chaque worker xdist
    worker_id = os.environ.get('PYTEST_XDIST_WORKER', '')
    test_database_url = f"{settings.SQLALCHEMY_DATABASE_URI}-test{worker_id}"
    engine = create_engine(test_database_url)

    # Ensure the test database is fresh
    if database_exists(engine.url):
        drop_database(engine.url)
    create_database(engine.url)

    # Create all tables in the test database
    # SQLModel.metadata.create_all(engine)

    # Apply migrations using Alembic
    alembic_cfg = Config("/app/alembic.ini")  # Path to your Alembic configuration file
    alembic_cfg.set_main_option("sqlalchemy.url", test_database_url)
    command.upgrade(alembic_cfg, "head")  # Apply all migrations to the latest state

    # Utilise les dumps SQL pour enrichir la base de test
    dump_files = glob.glob('/app/tests/fixtures/dumps/*.dump')

    for dump_file in dump_files:
        db_uri = str(test_database_url).replace("postgresql+psycopg://", "postgresql://")
        try:
            subprocess.run(f"pg_restore -d {db_uri} {dump_file}".split(" "), check=True)
        except subprocess.CalledProcessError as e:
            print(f"ERREUR lors de la restauration du dump {dump_file}: {e}")
            print(f"Code de sortie: {e.returncode}")
            print(f"Sortie: {e.output if hasattr(e, 'output') else 'Non disponible'}")
            raise e

    yield engine  # Provide the engine to tests

    # Teardown: Drop the test database after all tests have completed
    drop_database(engine.url)


@pytest.fixture(scope="function", autouse=True)
def session(db_engine, monkeypatch):
    # Connect to the database and begin a transaction
    connection = db_engine.connect()
    transaction = connection.begin()

    # Create a session bound to the connection
    session = Session(bind=connection)

    class MockSession:
        def __init__(self, *args, **kwargs):
            pass

        def __enter__(self):
            return session

        def __exit__(self, exc_type, exc_value, traceback):
            pass

    # Patch the 'get_session' function to return the shared session
    monkeypatch.setattr('core.db.get_session', MockSession)

    yield session  # Provide the session to the tests

    # Cleanup after tests
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def db(session):
    yield session  # Provide the shared session to tests that use 'db'

# User tokens

def client(user: User = None, base_url: str = "", external = False, token: str = None):
    # app.dependency_overrides[get_db] = lambda: db

    # Create the TestClient
    _client = TestClient(app)

    # Set the headers for authentication
    if user or token:
        if not token:
            token = get_token_for_user(user) if not external else user.token_ajout_photo

        _client.headers.update({
            "Authorization": f"Bearer {token}",
        })

    # Root URL
    _client.base_url = f"http://localhost{settings.API_V1_STR}{base_url}"  # Adding base_url attribute to the client

    return _client


# Fixtures
from tests.fixtures.users import *
from tests.fixtures.expediteurs import *
from tests.fixtures.destinations import *
from tests.fixtures.sites import *
from tests.fixtures.enveloppes import *
from tests.fixtures.regles_metier import *

# Pour éviter les vrais appels extérieur
@pytest.fixture(autouse=True)
def mock_external_services(monkeypatch):
    """Empêche les connexions réseau pendant les tests"""
    monkeypatch.delattr("requests.sessions.Session.request")
