import pytest
from datetime import datetime
from models.business import <PERSON>asier, Site, Enveloppe
from services.casier import ServiceCasier
from constants.enumerations import DestinationEnveloppeEnum, StatutCasierEnum


class TestServiceCasier:
    
    @pytest.fixture
    def site(self, db):
        site = Site(nom="Site Test")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site
    
    def test_obtenir_casier_disponible(self, db, site, user):
        """Test l'obtention d'un casier disponible"""
        
        # Créer un casier disponible
        casier = Casier(
            site_id=site.id,
            numero="TEST-1",
            capacite_max=10,
            statut=StatutCasierEnum.DISPONIBLE
        )
        db.add(casier)
        db.commit()
        
        # Tester l'obtention du casier
        casier_obtenu = ServiceCasier.obtenir_casier_disponible(site)
        
        # Vérifier que le casier obtenu est bien celui créé
        assert casier_obtenu is not None
        assert casier_obtenu.id == casier.id
        assert casier_obtenu.numero == "TEST-1"
        assert casier_obtenu.date_attribution is not None
        
        # Ajouter des enveloppes au casier
        for i in range(3):
            enveloppe = Enveloppe(
                casier_id=casier.id,
                site_id=site.id,
                user_id=user.id,
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE
            )
            db.add(enveloppe)
        db.commit()
        
        # Tester à nouveau l'obtention du casier
        casier_obtenu_apres = ServiceCasier.obtenir_casier_disponible(site)
        
        # Vérifier qu'aucun casier n'est retourné car le casier existant a des enveloppes
        assert casier_obtenu_apres is None

        # On supprime les enveloppes
        for enveloppe in casier.enveloppes:
            enveloppe.casier = None
            db.add(enveloppe)
        db.commit()

        # Casier libre
        casier_obtenu_apres = ServiceCasier.obtenir_casier_disponible(site)
        assert casier_obtenu.id == casier.id
