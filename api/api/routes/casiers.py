from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select, func, text
from typing import List, Dict, Any
from datetime import datetime, timedelta
from api.deps import SessionDep, CurrentUser, CurrentManagerUser
from models.business import <PERSON>asier, Enveloppe, Expediteur, Site, StatutEnveloppeEnum, LotExpediteur
from constants.enumerations import StatutCasierEnum
from sqlalchemy.orm import joinedload
from models.public import CasierPublic, LotExpediteurPublic, PaginatedResponse
from models.users import UserRole
from math import ceil


router = APIRouter()

@router.get("/disponibles", response_model=List[CasierPublic])
def get_casiers_disponibles(
    *,
    session: SessionDep,
    current_user: CurrentUser
) -> Any:
    """
    Récupère tous les casiers disponibles pour le site de l'utilisateur connecté.
    """
    if not current_user.site_id:
        raise HTTPException(
            status_code=400,
            detail="L'utilisateur n'est pas associé à un site"
        )
    
    statement = select(Casier).where(
        Casier.site_id == current_user.site_id,
        Casier.statut == StatutCasierEnum.DISPONIBLE
    )
    
    casiers = session.exec(statement).all()
    return casiers

@router.post("/", response_model=CasierPublic)
def create_casier(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    casier: Casier
) -> CasierPublic:
    """
    Crée un nouveau casier.
    """
    session.add(casier)

    # Si l'utilisateur est un user/manager, le Casier est créer sur son site
    if current_user.role not in [UserRole.ADMIN]:
        casier.site_id = current_user.site_id

    session.commit()
    session.refresh(casier)
    return casier

@router.get("/", response_model=PaginatedResponse[CasierPublic])
def read_casiers(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    skip: int = 0,
    limit: int = 10,
    search: str | None = None,
) -> PaginatedResponse[CasierPublic]:
    """
    Récupère la liste paginée des casiers.
    """
    base_query = select(Casier)

    # joins + eager‑loads
    base_query = base_query.options(
        joinedload(Casier.site),
        joinedload(Casier.lot_expediteur).joinedload(LotExpediteur.expediteur),
        joinedload(Casier.lot_expediteur).joinedload(LotExpediteur.enveloppes),
    )

    if search:
        pattern = f"%{search}%"
        base_query = (
            base_query.join(Casier.site).where(
                Casier.numero.ilike(pattern)
                | Casier.emplacement.ilike(pattern)
                | Site.nom.ilike(pattern)
            )
        )

    # ----- total -----
    total_items: int = session.exec(
        select(func.count()).select_from(base_query.subquery())  # count(*) over filtered sub‑query
    ).one()

    # ----- page slice -----
    page_items: List[Casier] = (
        session.exec(
            base_query.order_by(Casier.id).offset(skip).limit(limit)
        )
        .unique()
        .all()
    )

    # map to public models
    items_public: List[CasierPublic] = []
    for casier in page_items:
        items_public.append(
            CasierPublic(
                id=casier.id,
                numero=casier.numero,
                emplacement=casier.emplacement,
                site=casier.site,            
                site_id=casier.site_id,
                statut=casier.statut,
                lot_expediteur=(
                    LotExpediteurPublic.model_validate(
                        casier.lot_expediteur,
                        update={"nombre_enveloppes": len(casier.enveloppes)},
                    )
                    if casier.lot_expediteur
                    else None
                ),
            )
        )

    return PaginatedResponse[CasierPublic](
        items=items_public,
        current_page=(skip // limit) + 1,
        page_size=limit,
        total_items=total_items,
        total_pages=ceil(total_items / limit) if limit else 1,
    )


@router.get("/{casier_id}", response_model=Casier)
def read_casier(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    casier_id: int
) -> Casier:
    """
    Récupère un casier par son ID.
    """
    # Utiliser joinedload pour charger le lot expéditeur associé
    query = select(Casier).where(Casier.id == casier_id).options(
        joinedload(Casier.site),
        joinedload(Casier.lot_expediteur)
    )
    
    casier = session.exec(query).first()
    if not casier:
        raise HTTPException(status_code=404, detail="Casier non trouvé")
    return casier

@router.put("/{casier_id}", response_model=Casier)
def update_casier(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    casier_id: int,
    casier: Casier
) -> Casier:
    """
    Met à jour un casier existant.
    """
    db_casier = session.get(Casier, casier_id)
    if not db_casier:
        raise HTTPException(status_code=404, detail="Casier non trouvé")
    
    for key, value in casier.dict(exclude_unset=True).items():
        setattr(db_casier, key, value)
    
    session.add(db_casier)
    session.commit()
    session.refresh(db_casier)
    return db_casier

@router.delete("/{casier_id}", response_model=Casier)
def delete_casier(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    casier_id: int
) -> Casier:
    """
    Supprime un casier.
    """
    casier = session.get(Casier, casier_id)
    if not casier:
        raise HTTPException(status_code=404, detail="Casier non trouvé")
    
    # Vérifier si le casier est utilisé par des enveloppes
    enveloppes_count = session.query(func.count(Enveloppe.id)).filter(Enveloppe.casier_id == casier_id).scalar()
    if enveloppes_count > 0:
        raise HTTPException(status_code=400, detail="Impossible de supprimer le casier car il est utilisé par une ou plusieurs enveloppes")
    
    session.delete(casier)
    session.commit()
    return casier
