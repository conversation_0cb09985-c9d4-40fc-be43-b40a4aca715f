from fastapi import APIRouter, Depends
from typing import List
from api.deps import SessionDep, CurrentUser
from constants.enumerations import (
    ProduitEnum,
    StatutEnveloppeEnum,
    StatutLotExpediteurEnum,
    StatutCasierEnum,
    DestinationEnveloppeEnum
)
from models.public import CategorieAffranchissement
from constants.affranchissements.affranchissement import CATEGORIES_AFFRANCHISSEMENTS
from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT, NatureAffranchissementType

router = APIRouter()

@router.get("/produits", response_model=List[str])
def get_produits(
    *,
    session: SessionDep,
    current_user: CurrentUser
) -> List[str]:
    """
    Retourne la liste de tous les types de produits disponibles
    """
    return [produit.value for produit in ProduitEnum]

@router.get("/destinations", response_model=List[str])
def get_destinations(
    *,
    session: SessionDep,
    current_user: CurrentUser
) -> List[str]:
    """
    Retourne la liste de tous les types de produits disponibles
    """
    return [produit.value for produit in DestinationEnveloppeEnum]

@router.get("/affranchissements", response_model=List[CategorieAffranchissement])
def get_modeles_affranchissements(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> List[CategorieAffranchissement]:
    """
    Retourne la liste des catégories d'affranchissements avec leurs types
    """
    return list(CATEGORIES_AFFRANCHISSEMENTS.values())

@router.get("/natures", response_model=List[NatureAffranchissementType])
def get_natures(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> List[NatureAffranchissementType]:
    """
    Retrieve a list of Destinations with optional search filter
    """
    return list(NATURES_AFFRANCHISSEMENT.values())


@router.get("/enveloppes_status", response_model=List[str])
def get_enveloppes_status(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> List[str]:
    """
    Retrieve a list of Enveloppes status
    """
    return [status.value for status in StatutEnveloppeEnum]

@router.get("/lots_expediteur_statut", response_model=List[str])
def get_enveloppes_status(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> List[str]:
    """
    Retrieve a list of Enveloppes status
    """
    return [status.value for status in StatutLotExpediteurEnum]


@router.get("/casier_statut", response_model=List[str])
def get_enveloppes_status(
    *,
    session: SessionDep,
    current_user: CurrentUser,
) -> List[str]:
    """
    Retrieve a list of Enveloppes status
    """
    return [status.value for status in StatutCasierEnum]
