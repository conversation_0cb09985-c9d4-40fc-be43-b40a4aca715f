from fastapi import APIRouter, Depends, Query
from sqlmodel import Session, select, func, text
from typing import List, Dict, Any
from datetime import datetime, timedelta
from api.deps import SessionDep, CurrentUser
from models import Enveloppe, Expediteur, Site, StatutEnveloppeEnum
from sqlalchemy import bindparam



router = APIRouter()

@router.get("/metrics")
def get_dashboard_metrics(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    start_date: datetime = None,
    end_date: datetime = None,
    site_id: int = None
):
    """
    Récupère les métriques pour le dashboard
    """
    # Métriques des expéditeurs avec sous-affranchissement
    
    # Définir les dates par défaut si non fournies
    if not start_date:
        start_date = datetime.now() - timedelta(days=30)
    if not end_date:
        end_date = datetime.now()
        
    
    # Exécuter les requêtes
    params = {"start_date": start_date, "end_date": end_date}
    params["site_ids"] = [site_id] if site_id else session.query(Site.id).all()

    print(params)

    print({
    "start_date": start_date.isoformat() if isinstance(start_date, datetime) else start_date,
    "end_date": end_date.isoformat() if isinstance(end_date, datetime) else end_date,
    "site_ids": params["site_ids"]
})

    
    query_expediteurs = text(f"""
        SELECT 
            exp.nom as "nom",
            exp.adresse as "adresse",
            exp.coclico as "coclico",
            exp.siret as "siret",
            s.nom as "site",
            COUNT(e.id) as "plis",
            SUM((e.valorisation->'postage'->>'montant_sous_affranchissement')::float) as "sous_aff",
            SUM((e.valorisation->'livraison'->>'cout_total')::float) as "cas_a_deliver",
            SUM((e.valorisation->'collecte'->>'cout_ttc')::float) as "cas_b_collect_ttc",
            SUM((e.valorisation->'expédition'->>'cout_ttc')::float) as "cas_c_shipping_ttc"
        FROM expediteur exp
        LEFT JOIN enveloppe e ON e.expediteur_id = exp.id
        LEFT JOIN site s ON e.site_id = s.id
        WHERE e.valorisation IS NOT NULL
            AND (e.valorisation->'postage'->>'montant_sous_affranchissement')::float > 0
            AND e.created_at BETWEEN :start_date AND :end_date
            AND e.site_id IN :site_ids
            AND e.statut != 'EDITION'
        GROUP BY exp.id, exp.nom, exp.adresse, exp.coclico, exp.siret, s.nom
        HAVING SUM((e.valorisation->'postage'->>'montant_sous_affranchissement')::float) > 0
        ORDER BY exp.nom
    """).bindparams(bindparam("site_ids", expanding=True))
    
    
    # Top 10 des expéditeurs par somme cumulée des gains potentiels
    query_top10_expediteurs = text(f"""
        SELECT 
            exp.nom as "nom",
            COUNT(e.id) as "volume",
            SUM((e.valorisation->'postage'->>'montant_sous_affranchissement')::float) as "sous_aff",
            SUM((e.valorisation->'expédition'->>'cout_ttc')::float) as "cas_c_shipping_ttc",
            AVG((e.valorisation->'expédition'->>'cout_ttc')::float) as "moyenne_cas_c"               
        FROM expediteur exp
        LEFT JOIN enveloppe e ON e.expediteur_id = exp.id
        WHERE e.valorisation IS NOT NULL
            AND e.created_at BETWEEN :start_date AND :end_date
            AND e.site_id IN :site_ids
            AND e.statut != 'EDITION'
        GROUP BY exp.id
        ORDER BY "cas_c_shipping_ttc" DESC NULLS LAST
        LIMIT 10
    """).bindparams(bindparam("site_ids", expanding=True))
    
    # Somme des gains potentiels par personne par site
    query_gains_par_site = text(f"""
        SELECT 
            s.nom as "site",
            COUNT(e.id) as "volume",
            SUM((e.valorisation->'postage'->>'montant_sous_affranchissement')::float) as "sous_aff",
            AVG((e.valorisation->'postage'->>'montant_sous_affranchissement')::float) as "moyenne_sous_aff",
            SUM((e.valorisation->'expédition'->>'cout_ttc')::float) as "cas_c_shipping_ttc"
        FROM site s
        LEFT JOIN enveloppe e ON e.site_id = s.id
        WHERE e.valorisation IS NOT NULL
            AND e.created_at BETWEEN :start_date AND :end_date
            AND e.site_id IN :site_ids
            AND e.statut != 'EDITION'
        GROUP BY s.id, s.nom
        ORDER BY s.nom
    """).bindparams(bindparam("site_ids", expanding=True))
    
    # Nombre de plis stockés par expéditeur sur le site
    # query_plis_stockes = text("""
    #     SELECT 
    #         s.nom as "site",
    #         exp.nom as "expediteur",
    #         COUNT(e.id) as "plis_stockes"
    #     FROM site s
    #     LEFT JOIN enveloppe e ON e.site_id = s.id
    #     LEFT JOIN expediteur exp ON e.expediteur_id = exp.id
    #     WHERE e.statut IN ('STOCKEE', 'FRAUDULEUSE')
    #     GROUP BY s.id, s.nom, exp.id, exp.nom
    #     ORDER BY s.nom, "plis_stockes" DESC
    # """)
    
    # Nombre d'enveloppes terminées par jour
    query_enveloppes_par_jour = text(f"""
        SELECT 
            DATE(e.created_at) as jour,
            COUNT(e.id) as nombre
        FROM enveloppe e
        WHERE e.statut != 'EDITION'
            AND e.created_at BETWEEN :start_date AND :end_date
            AND e.site_id IN :site_ids
        GROUP BY DATE(e.created_at)
        ORDER BY jour
    """).bindparams(bindparam("site_ids", expanding=True))

    # Temps moyen de traitement par pli et par utilisateur
    query_temps_moyen_utilisateur = text(f"""
        WITH temps_traitement AS (
            SELECT
                e.user_id,
                e.id as enveloppe_id,
                EXTRACT(EPOCH FROM (e.completed_at - e.created_at)) / 60.0 as temps_minutes
            FROM
                enveloppe e
            WHERE
                e.created_at BETWEEN :start_date AND :end_date
                AND e.completed_at IS NOT NULL
                AND e.statut != 'EDITION'
                AND e.site_id IN :site_ids
        )

        SELECT
            u.id as user_id,
            u.email as user_email,
            COUNT(tt.enveloppe_id) as nombre_plis_completes,

            -- Temps moyen de traitement en minutes
            ROUND(AVG(tt.temps_minutes)::numeric, 2) as temps_moyen_minutes,

            -- Temps minimum de traitement en minutes
            ROUND(MIN(tt.temps_minutes)::numeric, 2) as temps_min_minutes,

            -- Temps maximum de traitement en minutes
            ROUND(MAX(tt.temps_minutes)::numeric, 2) as temps_max_minutes
        FROM
            "user" u
        LEFT JOIN
            temps_traitement tt ON tt.user_id = u.id
        WHERE
            tt.temps_minutes IS NOT NULL
        GROUP BY
            u.id, u.email
        HAVING
            COUNT(tt.enveloppe_id) > 0
    """).bindparams(bindparam("site_ids", expanding=True))


    
    result_expediteurs = session.execute(query_expediteurs, params).mappings().all()
    result_enveloppes = session.execute(query_enveloppes_par_jour, params).mappings().all()
    result_temps_moyen = session.execute(query_temps_moyen_utilisateur, params).mappings().all()
    result_top10 = session.execute(query_top10_expediteurs, params).mappings().all()
    result_gains_site = session.execute(query_gains_par_site, params).mappings().all()
    # result_plis_stockes = session.execute(query_plis_stockes).mappings().all()
    
    return {
        "expediteurs_sous_affranchissement": result_expediteurs,
        "enveloppes_par_jour": result_enveloppes,
        "temps_moyen_utilisateur": result_temps_moyen,
        "top10_expediteurs": result_top10,
        "gains_par_site": result_gains_site
    }
